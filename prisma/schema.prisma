generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model shortintro {
  id         String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  intro      String?  @db.VarChar(250)
  skills     String[]
  bio        String?
  profilepic String?  @db.VarChar(250)
}

model headline {
  id           String  @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  mainheadline String? @db.VarChar(250)
  subheadline  String? @db.VarChar(250)
}

model userrole {
  id          String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  logo        String?   @db.VarChar(250)
  title       String?   @db.VarChar(250)
  description String?
  create_at   DateTime? @default(now()) @db.Timestamp(6)
}

model category {
  id         String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title      String?   @db.Var<PERSON>har(250)
  created_at DateTime? @default(now()) @db.Timestamptz(6)
}
