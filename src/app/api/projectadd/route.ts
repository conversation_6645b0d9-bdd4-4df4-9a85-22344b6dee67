import { imgUploadArray } from "@/lib/cloudinaryhelper";
import { refactor } from "@/lib/helper";
import { NextRequest, NextResponse } from "next/server";

type DetailsType = {
    imgcontainer: File[] | null,
    description : string | null,
    title : string | null,
    github: string | null,
    live : string | null,
    parenttable: string
}

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const retrieve = [...formData.entries()];

    const refactorData = refactor(retrieve,"imgcontainer") as DetailsType;

    const imgContainer = await imgUploadArray(refactorData.imgcontainer);

    return NextResponse.json({data:imgContainer});
}