"use client";
import { InfoProvider } from "@/app/contextprovider/contextprovider"
import { useContext, useEffect, useState } from "react"

export default function AlertModal(){
    const context = useContext(InfoProvider);

    if(!context) throw new Error("context error");

    const {setModalInfo,modalInfo} = context;
    const [indexContainer,setIndexContainer] = useState<number[]>([]);

    const filterArray=(indexNum:number)=>{
        setTimeout(()=>{
            setIndexContainer(prev=>{
                if(prev.includes(indexNum)){
                    return;
                }else
            });
        },2000);

        setTimeout(()=>{
            setModalInfo(prev=>{
                return prev.slice(1)
            });

            setIndexContainer(prev=>{
                return prev.slice(1)
            })
        },3000)
    }
    return(
        <>
        <div className="fixed bottom-0 right-[30%] w-[30%]  z-40 flex flex-col gap-y-5">
            {modalInfo.map((items,index)=>{
                filterArray(index);
                return <div className={`translate-y-[100%] transition-all duration-150 ease-linear py-2.5 px-5 text-sky-600 rounded-xl bg-sky-300 ${indexContainer.includes(index)?"translate-x-[100%] opacity-0":"translate-y-0 opacity-100"}`} key={index}>
                        {items}
                </div>
            })}
        </div>
        </>
    )
}