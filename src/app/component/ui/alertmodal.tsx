"use client";
import { InfoProvider } from "@/app/contextprovider/contextprovider"
import { useContext, useEffect, useState } from "react"

export default function AlertModal(){
    const context = useContext(InfoProvider);

    if(!context) throw new Error("context error");

    const {setModalInfo,modalInfo} = context;
    const [arrayContaienr,setArrayContainer] = useState(modalInfo);

    const filterArray=(indexNum:number)=>{
        setTimeout(()=>{
            setModalInfo((prev)=>{
                const copy = prev.filter((_,index)=> indexNum !== index);

                return copy;
            })
        },2000)
    }
    return(
        <>
        <div className="fixed bottom-0 right-[30%] w-[30%]  z-40 flex flex-col gap-y-5">
            {modalInfo.map((items,index)=>{
                return <div className="transition-all duration-150 ease-linear py-2.5 px-5 text-sky-600 rounded-xl bg-sky-300" key={index} onClick={filterArray(index)}>
                        {items}
                </div>
            })}
        </div>
        </>
    )
}