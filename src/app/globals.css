@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

body::-webkit-scrollbar-track{
  background: #ecf0f1;
}

body::-webkit-scrollbar {
  width:5px;
}
/* ================ */
/* clientStart */
/* =============== */
/* sidenav */
.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
}
      
.custom-scrollbar::-webkit-scrollbar-track {
  background: #ecf0f1;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
}
      
.custom-scrollbar::-webkit-scrollbar-thumb, body::-webkit-scrollbar-thumb {
  background: var(--combineColor);
  border-radius: 10px;
  min-height: 40px;
}
/* introductionPage */
@keyframes swing6to0 {
  0%{transform:rotate(6deg)}
  50%{transform:rotate(0deg)}
  100%{transform:rotate(6deg)}
}
@keyframes textAnimation {
  from{width:0ch}
  to{width:var(--wordCh)}
}
@keyframes slideBack {
  from{width:var(--wordCh)}
  to{width:0ch}
}
@keyframes blinkCursor {
  0%, 50% { border-color: transparent; }
  51%, 100% { border-color: var(--combineColor); }
}
/* ================ */
/* clientFinish */
/* =============== */

/* ================ */
/* dashboardStrat */
/* =============== */
.sidenavScrollbar::-webkit-scrollbar{
  width:8px
}
.sidenavScrollbar::-webkit-scrollbar-corner{
  background-color:crimson;
}
.sidenavScrollbar::-webkit-scrollbar-track{
  background-color:rgba(189, 195, 199, 0.4);
  border-radius: 50px;
}
.sidenavScrollbar::-webkit-scrollbar-thumb{
  background-color:var(--combineColor);
  border-radius: 100px;
}
/* ================ */
/* dashboardFinish */
/* =============== */

/* ================ */
/* reusable */
/* =============== */
.loader {
  width: 40px;
  height: 40px;
  position: relative;
  --c:no-repeat linear-gradient(var(--combineColor) 0 0);
  background:
    var(--c) center/100% 10px,
    var(--c) center/10px 100%;
}
.loader:before {
  content:'';
  position: absolute;
  inset: 0;
  background:
    var(--c) 0    0,
    var(--c) 100% 0,
    var(--c) 0    100%,
    var(--c) 100% 100%;
  background-size: 15.5px 15.5px;
  animation: l16 1.5s infinite cubic-bezier(0.3,1,0,1);
}
@keyframes l16 {
   33%  {inset:-10px;transform: rotate(0deg)}
   66%  {inset:-10px;transform: rotate(90deg)}
   100% {inset:0    ;transform: rotate(90deg)}
}